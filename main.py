from fastapi import <PERSON><PERSON><PERSON>, File, UploadFile, HTTPException, Depends
from fastapi.responses import JSONResponse, HTMLResponse, RedirectResponse
from fastapi.security import OAuth2PasswordBearer
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
import google.genai as genai
from dotenv import load_dotenv
import os
import logging
import tempfile
from datetime import timedelta, datetime
# Import authentication modules
from core.security import create_access_token, verify_password, get_tenant_info
from core.database import get_tenant_id_and_name_from_slug, get_async_db_from_tenant_id
from models.security import OAuth2PasswordRequestFormWithClientID
from core.helper.mongo_helper import convert_objectid_to_str
from voice_cloning import router as voice_cloning_router
from app.v2.voice_cloning_v2 import router as inference_router
load_dotenv()

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# MongoDB collection names
TRANSCRIPTIONS_COLLECTION = "transcriptions"

app = FastAPI(title="Voice Processing API")
app.mount(path= "/v2",app = inference_router)
# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Allows all origins
    allow_credentials=True,
    allow_methods=["*"],  # Allows all methods
    allow_headers=["*"],  # Allows all headers
)

# Routes for web interface
@app.get("/", response_class=HTMLResponse)
async def root():
    # Redirect to login page
    return RedirectResponse(url="/docs")



def transcribe_audio_gemini(file_content: bytes, filename: str, api_key: str):
    # List of word items, each with the word text and its timestamp (if available)
    response_schema = {
        "type": "OBJECT",
        "properties": {
            "transcript": {
                "type": "ARRAY",
                "items": {
                    "type": "OBJECT",
                    "properties": {
                        "word": {"type": "STRING"},
                        "start_time": {"type": "STRING"},  # Model-generated timestamps as "00:00:05.230"
                        "end_time": {"type": "STRING"}
                    },
                    "required": ["word"]
                }
            }
        },
        "required": ["transcript"]
    }

    # Initialize Gemini client
    client = genai.Client(api_key=api_key)

    # Create a temporary file for upload
    with tempfile.NamedTemporaryFile(delete=False, suffix=os.path.splitext(filename)[1]) as temp_file:
        temp_file.write(file_content)
        temp_file_path = temp_file.name

    try:
        # Upload file to Gemini
        audio_file = client.files.upload(file=temp_file_path)

        # Generate transcription
        response = client.models.generate_content(
            model="gemini-2.5-flash-preview-05-20",
            contents=[audio_file, "Transcribe this audio into a word-level JSON transcript with start_time and end_time."],
            config={"response_mime_type": "application/json", "response_schema": response_schema}
        )
        return response.parsed
    finally:
        # Clean up temporary file
        os.unlink(temp_file_path)

@app.post("/login")
async def login(form_data: OAuth2PasswordRequestFormWithClientID = Depends()):
    # Find the database name of that tenant
    try:
        result = get_tenant_id_and_name_from_slug(form_data.client_id)

        tenant_id = str(result["_id"])
        tenant_database = get_async_db_from_tenant_id(tenant_id)

        # Connect to the user_collection of that database
        user = await tenant_database.users.find_one({"username": form_data.username})

        if not user:
            logger.error(f"User not found: {form_data.username}")
            raise HTTPException(status_code=401, detail="User not found")

        if not verify_password(form_data.password, user["hashed_password"]):
            logger.error(f"Incorrect credentials: {form_data.username}")
            raise HTTPException(status_code=401, detail="Incorrect credentials")

        # Create access token with user information
        access_token = create_access_token(
            data={"sub": user["username"], "role": user["role"], "tenant_id": tenant_id},
            expires_delta=timedelta(days=1)
        )

        # Convert ObjectId to string for JSON response
        user = convert_objectid_to_str(user)

        logger.info(f"User logged in: {user['username']} for tenant: {result['name']}")

        return {
            "id": user["_id"],
            "access_token": access_token,
            "token_type": "bearer",
            "username": user['username'],
            "role": user['role'],
            "tenant_id": tenant_id,
            "tenant_label": result["name"],
            "tenant_slug": form_data.client_id,
        }
    except Exception as e:
        logger.error(f"Login error: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Login failed: {str(e)}")

@app.get("/verify-token")
async def verify_token(user_tenant_info = Depends(get_tenant_info)):
    """If the token is valid, return the user's details
       If the token is invalid, get_tenant_info will raise an exception
    """
    return {"valid": True, "user": user_tenant_info.user.model_dump()}

@app.post("/transcribe")
async def transcribe(file: UploadFile = File(...), user_tenant_info = Depends(get_tenant_info)):
    try:
        contents = await file.read()

        # Get API key from tenant configuration (using same key as ElevenLabs)
        api_key = user_tenant_info.db.config.find_one({"name": "api-keys"})["value"]["GOOGLE_API_KEY"]
        # print(api_key)

        # Use Gemini for transcription
        transcription_result = transcribe_audio_gemini(contents, file.filename, api_key)

        # Get tenant database
        tenant_database = get_async_db_from_tenant_id(user_tenant_info.tenant_id)

        # Extract text from Gemini response for storage
        transcript_text = " ".join([word_item["word"] for word_item in transcription_result.get("transcript", [])])

        # Prepare transcription document
        transcription_doc = {
            "text": transcript_text,
            "file_name": file.filename,
            "file_size": len(contents),
            "username": user_tenant_info.user.username,
            "created_at": datetime.utcnow(),
            "metadata": transcription_result
        }

        # Store in transcriptions collection
        await tenant_database.transcriptions.insert_one(transcription_doc)

        return JSONResponse(content=transcription_result, status_code=200)
    except Exception as e:
        logger.error(f"Transcription error: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

# Include the voice cloning router
app.include_router(voice_cloning_router)

# Include the TTS router
from tts import router as tts_router
app.include_router(tts_router)
