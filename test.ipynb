import google.generativeai as genai

genai.configure(api_key="AIzaSyBuzZKNMW-Cp3VjCeMebVZIU0FkMvtjEWE")

# List of word items, each with the word text and its timestamp (if available)
response_schema = {
    "type": "OBJECT",
    "properties": {
        "transcript": {
            "type": "ARRAY",
            "items": {
                "type": "OBJECT",
                "properties": {
                    "word": {"type": "STRING"},
                    "start_time": {"type": "STRING"},  # Model-generated timestamps as "00:00:05.230"
                    "end_time": {"type": "STRING"}
                },
                "required": ["word"]
            }
        }
    },
    "required": ["transcript"]
}


import google.genai as genai

# Initialize client with your Gemini API key
client = genai.Client(api_key="AIzaSyBuzZKNMW-Cp3VjCeMebVZIU0FkMvtjEWE")

audio_file = client.files.upload(file="/Users/<USER>/Voice_Process/1746690554.133380 (2).wav")


response = client.models.generate_content(
    model="gemini-2.5-flash",  # or the available multimodal model
    contents=[
        audio_file,
        "Transcribe this audio into a word-level JSON transcript with start_time and end_time."
    ],
    config={
        "response_mime_type": "application/json",
        "response_schema": response_schema,
    }
)

print(response.text)    # JSON output
print(response.parsed)  # Parsed Python object if availableb






